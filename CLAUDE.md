# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a web-based bill analysis system (账单分析系统) built with Flask that allows users to upload CSV financial records, analyze spending patterns, and visualize financial data. The system includes user authentication, data visualization, and account management features.

## Architecture

### Backend Structure
- **app.py**: Main Flask application with all routes and business logic (1600+ lines)
- **config.py**: Centralized configuration management using environment variables
- **Database**: MySQL with pymysql driver
- **Authentication**: Session-based with email verification via SMTP

### Frontend Structure
- **Templates**: Located in `app/templates/` (HTML with Bootstrap 5)
  - `index.html`: Main landing page with file upload
  - `analyze.html`: Data visualization and analysis page
  - `login.html` & `register.html`: User authentication
  - `error.html`: Error handling page
- **Static Assets**: Located in `app/static/`
  - CSS, JavaScript, images, and user uploads

### Database Schema
- **users**: User accounts with email verification
- **verification_codes**: Email verification system
- **bill_records**: Financial transaction records
- **upload_records**: File upload history

## Development Commands

### Setup and Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python app.py
```

### Database Management
```bash
# Initialize MySQL database
python setup_mysql.py

# Test database connection
python test_connection.py

# Validate configuration
python validate_config.py
```

### Configuration
The system uses environment variables managed through `config.py`. Key configurations:
- MySQL connection settings
- SMTP email settings  
- Flask application settings
- File upload and security parameters

## Key Features

### Data Processing
- CSV file upload with automatic encoding detection
- Skips first 10 rows of CSV files (configurable via `CSV_SKIP_ROWS`)
- Supports multiple CSV encodings using chardet
- Automatic data type conversion and validation

### User Management
- Email-based registration with verification codes
- Session management with secure cookies
- Avatar upload with image processing (PIL/Pillow)
- Password reset via email verification

### Data Analysis
- Financial categorization and trend analysis
- Multiple chart types (ECharts integration on frontend)
- Export functionality for user data
- Ranking system for income/expense categories

## Development Notes

### File Processing Pipeline
1. Files uploaded to user-specific directories under `uploads/`
2. CSV processing starts from line 11 (skips header rows)
3. Data saved to MySQL with user isolation
4. Temporary files cleaned up after processing

### Security Considerations
- Password hashing with Werkzeug
- Input validation and sanitization
- User data isolation in database
- Session-based authentication
- File type restrictions for uploads

### Error Handling
- Comprehensive exception handling throughout app.py
- Database connection retry logic
- User-friendly error messages
- Detailed logging for debugging

## Common Development Tasks

When working with this codebase:
1. Always test database connections before making changes
2. Use the configuration system in `config.py` for environment settings
3. Follow the existing error handling patterns
4. Test file upload functionality with various CSV formats
5. Verify email functionality is working for user registration