<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账单分析 - 结果</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: "Microsoft YaHei", sans-serif;
        }
        .container {
            padding-top: 30px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 6px 10px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            border: none;
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 15px 20px;
            border-radius: 15px 15px 0 0 !important;
        }
        .card-body {
            padding: 20px;
        }
        .summary-item {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        .summary-item h3 {
            margin: 10px 0;
            font-size: 24px;
        }
        .summary-item p {
            font-size: 14px;
            margin: 0;
            color: #6c757d;
        }
        .income {
            background-color: rgba(25, 135, 84, 0.1);
            color: #198754;
        }
        .expense {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }
        .balance {
            background-color: rgba(13, 110, 253, 0.1);
            color: #0d6efd;
        }
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }
        .table-container {
            overflow-x: auto;
        }
        .table {
            font-size: 14px;
        }
        .nav-pills .nav-link.active {
            background-color: #0d6efd;
        }
        .nav-pills .nav-link {
            color: #6c757d;
        }
        .search-container {
            margin-bottom: 20px;
        }
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 15px;
        }
        .back-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        /* 排序切换按钮样式 */
        #sortToggleBtn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            border-radius: 0.2rem;
            transition: all 0.2s ease-in-out;
            min-width: 70px;
        }
        #sortToggleBtn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }
        #sortToggleBtn i {
            margin-right: 4px;
        }
        .card-header .d-flex.gap-2 {
            gap: 0.5rem !important;
        }
        /* 排行榜卡片体样式优化 */
        #rankingChart {
            margin-top: -10px;
        }
        .card-body {
            padding-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">账单分析结果</h1>
        
        <!-- 总览卡片 -->
        <div class="row">
            <div class="col-md-4">
                <div class="summary-item income">
                    <i class="bi bi-wallet2 fs-1"></i>
                    <h3 id="totalIncome">¥0.00</h3>
                    <p>总收入</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="summary-item expense">
                    <i class="bi bi-cash-stack fs-1"></i>
                    <h3 id="totalExpense">¥0.00</h3>
                    <p>总支出</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="summary-item balance">
                    <i class="bi bi-piggy-bank fs-1"></i>
                    <h3 id="balance">¥0.00</h3>
                    <p>结余</p>
                </div>
            </div>
        </div>
        
        <!-- 修改导航选项卡，添加排行榜选项 -->
        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="pills-charts-tab" data-bs-toggle="pill" data-bs-target="#pills-charts" type="button" role="tab" aria-controls="pills-charts" aria-selected="true">图表分析</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="pills-ranking-tab" data-bs-toggle="pill" data-bs-target="#pills-ranking" type="button" role="tab" aria-controls="pills-ranking" aria-selected="false">排行榜</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="pills-data-tab" data-bs-toggle="pill" data-bs-target="#pills-data" type="button" role="tab" aria-controls="pills-data" aria-selected="false">账单明细</button>
            </li>
        </ul>
        
        <div class="tab-content" id="pills-tabContent">
            <!-- 图表分析标签页 -->
            <div class="tab-pane fade show active" id="pills-charts" role="tabpanel" aria-labelledby="pills-charts-tab">
                <div class="row">
                    <!-- 分类占比饼图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">支出分类占比</h5>
                            </div>
                            <div class="card-body">
                                <div id="categoryPieChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 收支类型占比饼图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">收支类型占比</h5>
                            </div>
                            <div class="card-body">
                                <div id="typePieChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- 趋势图 -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">收支趋势</h5>
                            </div>
                            <div class="card-body">
                                <div id="trendChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 新增图表分析 -->
                <div class="row">
                    <!-- 月度收支对比柱状图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">月度收支对比</h5>
                            </div>
                            <div class="card-body">
                                <div id="monthlyChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 账户分布饼图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">账户资金分布</h5>
                            </div>
                            <div class="card-body">
                                <div id="accountChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- 周收支热力图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">周收支活跃度</h5>
                            </div>
                            <div class="card-body">
                                <div id="weeklyHeatmap" class="chart-container"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 收支累计图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">收支累计趋势</h5>
                            </div>
                            <div class="card-body">
                                <div id="cumulativeChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- 分类收支对比雷达图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">分类收支雷达图</h5>
                            </div>
                            <div class="card-body">
                                <div id="radarChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 时段分析柱状图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">时段消费分析</h5>
                            </div>
                            <div class="card-body">
                                <div id="hourlyChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 修改排行榜标签页结构 -->
            <div class="tab-pane fade" id="pills-ranking" role="tabpanel" aria-labelledby="pills-ranking-tab">
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="btn-toolbar justify-content-center" role="toolbar">
                            <div class="btn-group" role="group" aria-label="切换排行榜类型">
                                <button type="button" class="btn btn-primary active px-4 py-2" id="expenseRankingBtn">
                                    <i class="bi bi-cash-stack me-1"></i>支出排行榜
                                </button>
                                <button type="button" class="btn btn-outline-primary px-4 py-2" id="incomeRankingBtn">
                                    <i class="bi bi-wallet2 me-1"></i>收入排行榜
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0" id="rankingTitle">支出排行榜</h5>
                                <div class="d-flex align-items-center gap-2">
                                    <!-- 排序切换按钮 -->
                                    <button type="button" class="btn btn-outline-secondary btn-sm" id="sortToggleBtn" title="点击切换排序方式">
                                        <i class="bi bi-sort-numeric-down" id="sortIcon"></i>
                                        <span id="sortText">降序</span>
                                    </button>
                                    <span class="badge bg-info" id="rankingCount">0项</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- 添加Loading指示器 -->
                                <div id="rankingLoading" class="text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载排行榜数据...</p>
                                </div>
                                
                                <!-- 图表容器 -->
                                <div id="rankingChart" class="chart-container" style="width: 100%; min-height: 600px;"></div>
                                
                                <!-- 错误信息容器 -->
                                <div id="rankingError" class="alert alert-danger" style="display: none;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 账单明细标签页 -->
            <div class="tab-pane fade" id="pills-data" role="tabpanel" aria-labelledby="pills-data-tab">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">账单明细</h5>
                    </div>
                    <div class="card-body">
                        <div class="search-container">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="searchText" placeholder="搜索备注...">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="categoryFilter">
                                        <option value="">全部分类</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="typeFilter">
                                        <option value="">全部收支类型</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-primary" id="searchBtn">搜索</button>
                                    <button class="btn btn-secondary" id="resetBtn">重置</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="table-container">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th scope="col">时间</th>
                                        <th scope="col">分类</th>
                                        <th scope="col">收支类型</th>
                                        <th scope="col">金额</th>
                                        <th scope="col">备注</th>
                                        <th scope="col">账户</th>
                                        <th scope="col">来源</th>
                                    </tr>
                                </thead>
                                <tbody id="dataTableBody">
                                    <!-- 数据将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="pagination-container">
                            <nav aria-label="Page navigation">
                                <ul class="pagination" id="pagination">
                                    <!-- 分页将通过JavaScript填充 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <a href="/" class="btn btn-primary rounded-circle back-btn">
        <i class="bi bi-house-door"></i>
    </a>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取汇总数据
            fetch('/api/summary')
                .then(response => response.json())
                .then(data => {
                    // 更新摘要信息
                    document.getElementById('totalIncome').textContent = '¥' + data.total_income.toFixed(2);
                    document.getElementById('totalExpense').textContent = '¥' + data.total_expense.toFixed(2);
                    document.getElementById('balance').textContent = '¥' + data.balance.toFixed(2);
                    
                    // 初始化图表
                    initCategoryChart(data.category_summary);
                    initTypeChart(data.type_summary);
                    initTrendChart(data.daily_trend);

                    // 初始化新增图表
                    initMonthlyChart(data.monthly_summary);
                    initAccountChart(data.account_summary);
                    initWeeklyHeatmap(data.weekly_summary);
                    initCumulativeChart(data.cumulative_trend);
                    initRadarChart(data.category_summary, data.type_summary);
                    initHourlyChart(data.hourly_summary);
                })
                .catch(error => {
                    console.error('获取汇总数据出错:', error);
                });
            
            // 获取明细数据
            let allData = [];
            let currentPage = 1;
            const pageSize = 20;
            
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    allData = data;
                    
                    // 填充过滤器选项
                    fillFilterOptions(data);
                    
                    // 首次加载数据
                    updateTableData();
                })
                .catch(error => {
                    console.error('获取明细数据出错:', error);
                });
            
            // 搜索和重置按钮
            document.getElementById('searchBtn').addEventListener('click', function() {
                currentPage = 1;
                updateTableData();
            });
            
            document.getElementById('resetBtn').addEventListener('click', function() {
                document.getElementById('searchText').value = '';
                document.getElementById('categoryFilter').value = '';
                document.getElementById('typeFilter').value = '';
                currentPage = 1;
                updateTableData();
            });
            
            // 填充过滤器选项
            function fillFilterOptions(data) {
                const categorySet = new Set();
                const typeSet = new Set();
                
                data.forEach(item => {
                    if (item.分类) categorySet.add(item.分类);
                    if (item.收支类型) typeSet.add(item.收支类型);
                });
                
                const categoryFilter = document.getElementById('categoryFilter');
                categorySet.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category;
                    categoryFilter.appendChild(option);
                });
                
                const typeFilter = document.getElementById('typeFilter');
                typeSet.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    typeFilter.appendChild(option);
                });
            }
            
            // 更新表格数据
            function updateTableData() {
                const searchText = document.getElementById('searchText').value.toLowerCase();
                const categoryFilter = document.getElementById('categoryFilter').value;
                const typeFilter = document.getElementById('typeFilter').value;
                
                // 过滤数据
                const filteredData = allData.filter(item => {
                    const matchSearch = item.备注 && item.备注.toLowerCase().includes(searchText);
                    const matchCategory = !categoryFilter || item.分类 === categoryFilter;
                    const matchType = !typeFilter || item.收支类型 === typeFilter;
                    return matchSearch && matchCategory && matchType;
                });
                
                // 分页
                const start = (currentPage - 1) * pageSize;
                const end = start + pageSize;
                const pageData = filteredData.slice(start, end);
                
                // 更新表格
                const tableBody = document.getElementById('dataTableBody');
                tableBody.innerHTML = '';
                
                pageData.forEach(item => {
                    const tr = document.createElement('tr');
                    
                    // 格式化时间
                    const date = new Date(item.记录时间);
                    const formattedDate = date.toLocaleString('zh-CN');
                    
                    // 设置行样式
                    if (item.收支类型 && item.收支类型.includes('收入')) {
                        tr.classList.add('table-success');
                    } else if (item.收支类型 && item.收支类型.includes('支出')) {
                        tr.classList.add('table-danger');
                    }
                    
                    tr.innerHTML = `
                        <td>${formattedDate}</td>
                        <td>${item.分类 || '-'}</td>
                        <td>${item.收支类型 || '-'}</td>
                        <td>¥${item.金额 ? item.金额.toFixed(2) : '0.00'}</td>
                        <td>${item.备注 || '-'}</td>
                        <td>${item.账户 || '-'}</td>
                        <td>${item.来源 || '-'}</td>
                    `;
                    
                    tableBody.appendChild(tr);
                });
                
                // 更新分页
                updatePagination(filteredData.length);
            }
            
            // 更新分页控件
            function updatePagination(totalItems) {
                const totalPages = Math.ceil(totalItems / pageSize);
                const pagination = document.getElementById('pagination');
                pagination.innerHTML = '';
                
                // 添加上一页按钮
                const prevLi = document.createElement('li');
                prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
                prevLi.innerHTML = '<a class="page-link" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>';
                prevLi.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage > 1) {
                        currentPage--;
                        updateTableData();
                    }
                });
                pagination.appendChild(prevLi);
                
                // 添加页码按钮
                const maxPageButtons = 5;
                const startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
                const endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
                
                for (let i = startPage; i <= endPage; i++) {
                    const li = document.createElement('li');
                    li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                    li.innerHTML = `<a class="page-link" href="#">${i}</a>`;
                    li.addEventListener('click', function(e) {
                        e.preventDefault();
                        currentPage = i;
                        updateTableData();
                    });
                    pagination.appendChild(li);
                }
                
                // 添加下一页按钮
                const nextLi = document.createElement('li');
                nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
                nextLi.innerHTML = '<a class="page-link" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>';
                nextLi.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage < totalPages) {
                        currentPage++;
                        updateTableData();
                    }
                });
                pagination.appendChild(nextLi);
            }
            
            // 初始化分类饼图
            function initCategoryChart(categoryData) {
                const chartDom = document.getElementById('categoryPieChart');
                const myChart = echarts.init(chartDom);



                if (!categoryData || Object.keys(categoryData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无支出数据',
                            left: 'center',
                            top: 'center',
                            textStyle: {
                                color: '#999',
                                fontSize: 16
                            }
                        }
                    });
                    return;
                }

                // 转换数据为echarts需要的格式，优化数据过滤逻辑
                const seriesData = [];
                for (const [category, amount] of Object.entries(categoryData)) {
                    const numAmount = parseFloat(amount);
                    // 显示所有有意义的支出数据（包括负数和正数，但排除接近0的值）
                    if (Math.abs(numAmount) > 0.01) {
                        // 如果是负数，说明是支出，取绝对值
                        // 如果是正数，可能是某些特殊分类的支出，也包含进来
                        seriesData.push({
                            name: category || '未分类',
                            value: Math.abs(numAmount)
                        });
                    }
                }

                if (seriesData.length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无支出数据',
                            left: 'center',
                            top: 'center',
                            textStyle: {
                                color: '#999',
                                fontSize: 16
                            }
                        }
                    });
                    return;
                }

                // 对数据进行排序
                seriesData.sort((a, b) => b.value - a.value);

                // 只显示前8个类别，其余归为"其他"
                let others = 0;
                const top8 = seriesData.slice(0, 8);

                if (seriesData.length > 8) {
                    for (let i = 8; i < seriesData.length; i++) {
                        others += seriesData[i].value;
                    }
                    if (others > 0) {
                        top8.push({
                            name: '其他',
                            value: others
                        });
                    }
                }

                // 定义更丰富的颜色方案
                const colors = [
                    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
                    '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE'
                ];

                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: function(params) {
                            return `${params.seriesName}<br/>${params.name}: ¥${params.value.toFixed(2)} (${params.percent}%)`;
                        },
                        backgroundColor: 'rgba(50,50,50,0.9)',
                        borderColor: '#777',
                        borderWidth: 1,
                        textStyle: {
                            color: '#fff'
                        }
                    },
                    legend: {
                        orient: 'vertical',
                        right: '5%',
                        top: 'center',
                        data: top8.map(item => item.name),
                        textStyle: {
                            fontSize: 12
                        },
                        itemWidth: 14,
                        itemHeight: 14
                    },
                    series: [
                        {
                            name: '支出分类',
                            type: 'pie',
                            radius: ['45%', '75%'],
                            center: ['40%', '50%'],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 8,
                                borderColor: '#fff',
                                borderWidth: 2
                            },
                            label: {
                                show: true,
                                position: 'outside',
                                formatter: '{b}\n{d}%',
                                fontSize: 11,
                                color: '#666'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: 14,
                                    fontWeight: 'bold'
                                },
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            },
                            labelLine: {
                                show: true,
                                length: 15,
                                length2: 10
                            },
                            data: top8,
                            color: colors
                        }
                    ]
                };

                myChart.setOption(option);

                // 响应窗口大小变化
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            }
            
            // 初始化收支类型饼图
            function initTypeChart(typeData) {
                const chartDom = document.getElementById('typePieChart');
                const myChart = echarts.init(chartDom);

                if (!typeData || Object.keys(typeData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无收支数据',
                            left: 'center',
                            top: 'center',
                            textStyle: {
                                color: '#999',
                                fontSize: 16
                            }
                        }
                    });
                    return;
                }

                // 转换数据为echarts需要的格式，并按类型分类
                const seriesData = [];
                const typeColors = {
                    '收入': '#4CAF50',        // 清新绿色 - 代表收入增长
                    '支出': '#FF7043',        // 温暖橙红 - 比纯红色更柔和
                    '内部收入': '#42A5F5',    // 天空蓝 - 清爽明亮
                    '内部支出': '#FFA726',    // 琥珀橙 - 温暖但不刺眼
                    '资金收入': '#AB47BC',    // 优雅紫 - 高端感
                    '资金支出': '#EC407A',    // 玫瑰粉 - 柔和的粉色
                    '投资收入': '#26A69A',    // 青绿色 - 稳重可靠
                    '投资支出': '#FFCA28',    // 金黄色 - 明亮但不刺眼
                    '不计收支': '#78909C'     // 蓝灰色 - 中性且现代
                };

                for (const [type, amount] of Object.entries(typeData)) {
                    const numAmount = parseFloat(amount);
                    if (Math.abs(numAmount) > 0.01) {
                        const baseColor = typeColors[type] || '#95a5a6';
                        seriesData.push({
                            name: type || '未知类型',
                            value: Math.abs(numAmount),
                            itemStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0, y: 0, x2: 1, y2: 1,
                                    colorStops: [
                                        { offset: 0, color: baseColor },
                                        { offset: 1, color: baseColor + 'CC' } // 添加透明度
                                    ]
                                },
                                borderColor: '#fff',
                                borderWidth: 3,
                                shadowBlur: 8,
                                shadowColor: baseColor + '40'
                            }
                        });
                    }
                }

                if (seriesData.length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无收支数据',
                            left: 'center',
                            top: 'center',
                            textStyle: {
                                color: '#999',
                                fontSize: 16
                            }
                        }
                    });
                    return;
                }

                // 按金额排序
                seriesData.sort((a, b) => b.value - a.value);

                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: function(params) {
                            return `${params.seriesName}<br/>${params.name}: ¥${params.value.toFixed(2)} (${params.percent}%)`;
                        },
                        backgroundColor: 'rgba(50,50,50,0.9)',
                        borderColor: '#777',
                        borderWidth: 1,
                        textStyle: {
                            color: '#fff'
                        }
                    },
                    legend: {
                        orient: 'vertical',
                        right: '5%',
                        top: 'center',
                        data: seriesData.map(item => item.name),
                        textStyle: {
                            fontSize: 12
                        },
                        itemWidth: 14,
                        itemHeight: 14
                    },
                    series: [
                        {
                            name: '收支类型',
                            type: 'pie',
                            radius: ['50%', '80%'],
                            center: ['40%', '50%'],
                            avoidLabelOverlap: false,
                            label: {
                                show: true,
                                position: 'outside',
                                formatter: function(params) {
                                    // 根据类型显示不同的图标
                                    let icon = '';
                                    if (params.name.includes('收入')) {
                                        icon = '💰 ';
                                    } else if (params.name.includes('支出')) {
                                        icon = '💸 ';
                                    } else {
                                        icon = '📊 ';
                                    }
                                    return icon + params.name + '\n' + params.percent + '%';
                                },
                                fontSize: 12,
                                color: '#555',
                                fontWeight: '500'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: 15,
                                    fontWeight: 'bold',
                                    color: '#333'
                                },
                                itemStyle: {
                                    shadowBlur: 15,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.3)'
                                },
                                scale: true,
                                scaleSize: 5
                            },
                            labelLine: {
                                show: true,
                                length: 20,
                                length2: 15,
                                lineStyle: {
                                    color: '#999',
                                    width: 2
                                }
                            },
                            data: seriesData
                        }
                    ]
                };

                myChart.setOption(option);

                // 响应窗口大小变化
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            }
            
            // 初始化趋势图
            function initTrendChart(trendData) {
                const chartDom = document.getElementById('trendChart');
                const myChart = echarts.init(chartDom);
                
                // 转换数据为echarts需要的格式
                const dates = Object.keys(trendData).sort();
                const values = dates.map(date => trendData[date]);
                
                const option = {
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            return `${params[0].axisValue}<br/>金额: ¥${params[0].data.toFixed(2)}`;
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: dates
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: '¥{value}'
                        }
                    },
                    series: [
                        {
                            data: values,
                            type: 'line',
                            smooth: true,
                            lineStyle: {
                                width: 3
                            },
                            markPoint: {
                                data: [
                                    { type: 'max', name: '最大值' },
                                    { type: 'min', name: '最小值' }
                                ]
                            }
                        }
                    ]
                };
                
                myChart.setOption(option);
                
                // 响应窗口大小变化
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            }
            
            // 获取排行榜数据
            let rankingData = {
                income: [],
                expense: []
            };
            let currentRankingType = 'expense';  // 默认显示支出排行榜
            let currentSortOrder = 'desc';  // 默认降序排列 ('desc' 或 'asc')
            let rankingChart = null; // 全局变量保存图表实例

            // 显示加载状态
            function showRankingLoading() {
                document.getElementById('rankingLoading').style.display = 'block';
                document.getElementById('rankingChart').style.display = 'none';
                document.getElementById('rankingError').style.display = 'none';
            }

            // 显示排行榜图表
            function showRankingChart() {
                document.getElementById('rankingLoading').style.display = 'none';
                document.getElementById('rankingChart').style.display = 'block';
                document.getElementById('rankingError').style.display = 'none';
            }

            // 显示排行榜错误
            function showRankingError(message) {
                document.getElementById('rankingLoading').style.display = 'none';
                document.getElementById('rankingChart').style.display = 'none';
                document.getElementById('rankingError').style.display = 'block';
                document.getElementById('rankingError').textContent = message;
            }

            // 更新排行榜计数
            function updateRankingCount(type) {
                const count = rankingData[type] ? rankingData[type].length : 0;
                document.getElementById('rankingCount').textContent = `${count}项`;
            }

            // 添加调试信息
            console.log("开始获取排行榜数据...");
            showRankingLoading();

            fetch('/api/ranking')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    console.log("获取排行榜数据成功，正在解析JSON...");
                    return response.json();
                })
                .then(data => {
                    console.log("排行榜数据解析完成:", data);
                    rankingData = data;
                    
                    // 检查数据完整性
                    if (!data.income || !data.expense) {
                        console.error("排行榜数据格式不正确:", data);
                        showRankingError("数据格式不正确，无法显示排行榜");
                        return;
                    }
                    
                    console.log(`收入数据项数: ${data.income.length}, 支出数据项数: ${data.expense.length}`);
                    updateRankingCount('expense'); // 默认显示支出排行榜计数
                    
                    // 初始化排行榜
                    document.addEventListener('DOMContentLoaded', () => {
                        setTimeout(() => {
                            console.log("DOM加载完成，初始化排行榜");
                            setupRankingEvents();
                            updateRankingTitle();
                            updateSortButton();
                            initRankingChart(currentRankingType);
                        }, 500);
                    });

                    // 如果文档已经加载完成，直接初始化
                    if (document.readyState === 'complete' || document.readyState === 'interactive') {
                        console.log("文档已加载，直接初始化排行榜");
                        setTimeout(() => {
                            setupRankingEvents();
                            updateRankingTitle();
                            updateSortButton();
                            initRankingChart(currentRankingType);
                        }, 500);
                    }
                })
                .catch(error => {
                    console.error('获取排行榜数据出错:', error);
                    showRankingError(`获取排行榜数据失败: ${error.message}`);
                });
            
            // 重写排行榜初始化函数
            function initRankingChart(type) {
                console.log(`初始化${type}排行榜...`);
                const chartDom = document.getElementById('rankingChart');
                if (!chartDom) {
                    console.error('找不到rankingChart元素');
                    return;
                }
                
                // 显示加载状态
                showRankingLoading();
                
                try {
                    // 销毁之前的实例
                    if (rankingChart) {
                        rankingChart.dispose();
                        rankingChart = null;
                    }
                    
                    // 根据数据量动态设置图表高度
                    const dataLength = rankingData[type] ? rankingData[type].length : 0;
                    const minHeight = 600;
                    const itemHeight = 40; // 每个条目的高度
                    const dynamicHeight = Math.max(minHeight, dataLength * itemHeight + 200);
                    chartDom.style.height = dynamicHeight + 'px';
                    chartDom.style.width = '100%';
                    
                    // 如果没有数据，显示空图表
                    if (!rankingData[type] || rankingData[type].length === 0) {
                        console.log(`${type}排行榜没有数据`);
                        showRankingError(`没有${type === 'income' ? '收入' : '支出'}数据可供显示`);
                        return;
                    }
                    
                    // 准备数据
                    console.log(`处理${type}排行榜数据...`);
                    const sortedData = [...rankingData[type]];

                    // 根据当前排序方式对数据进行排序
                    if (currentSortOrder === 'desc') {
                        sortedData.sort((a, b) => b.金额 - a.金额);  // 降序：从大到小
                    } else {
                        sortedData.sort((a, b) => a.金额 - b.金额);  // 升序：从小到大
                    }
                    console.log(`${currentSortOrder === 'desc' ? '降序' : '升序'}排序后的${type}数据:`, sortedData);

                    // 显示所有数据（不限制数量）
                    console.log(`显示所有 ${sortedData.length} 项${type}数据`);

                    // 反转数组，让柱状图从下到上排序
                    const reversedData = [...sortedData].reverse();
                    
                    // 从数据中提取类别和值
                    const categories = reversedData.map(item => item.处理后备注 || '未知类别');
                    const values = reversedData.map(item => {
                        let amount = 0;
                        
                        try {
                            if (typeof item.金额 === 'number') {
                                amount = parseFloat(item.金额.toFixed(2));
                            } else if (typeof item.金额 === 'string') {
                                amount = parseFloat(parseFloat(item.金额).toFixed(2));
                            }
                        } catch (e) {
                            console.error('处理金额时出错:', e);
                        }
                        
                        return amount || 0;
                    });
                    
                    console.log(`${type}排行榜类别:`, categories);
                    console.log(`${type}排行榜数值:`, values);
                    
                    // 计算最大值，用于设置坐标轴
                    const maxValue = Math.max(...values, 1);  // 至少为1，避免全0的情况
                    
                    // 显示图表容器
                    showRankingChart();
                    
                    // 重新初始化ECharts实例
                    setTimeout(() => {
                        rankingChart = echarts.init(chartDom);
                        console.log("ECharts实例创建成功");
                        
                        // 设置图表选项
                        const option = {
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                },
                                formatter: function(params) {
                                    const data = params[0];
                                    return `${data.name}<br/>${type === 'income' ? '收入' : '支出'}: ¥${data.value.toFixed(2)}`;
                                }
                            },
                            legend: {
                                data: [type === 'income' ? '收入' : '支出'],
                                bottom: '5%'
                            },
                            grid: {
                                left: '15%',
                                right: '20%',
                                bottom: '10%',
                                top: '2%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'value',
                                position: 'top',
                                name: '金额(元)',
                                nameTextStyle: {
                                    color: '#666',
                                    fontSize: 12
                                },
                                axisLabel: {
                                    formatter: function(value) {
                                        if (value >= 10000) {
                                            return '¥' + (value / 10000).toFixed(1) + 'w';
                                        } else if (value >= 1000) {
                                            return '¥' + (value / 1000).toFixed(1) + 'k';
                                        }
                                        return '¥' + value.toFixed(0);
                                    },
                                    color: '#666',
                                    fontSize: 11
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: '#e0e0e0'
                                    }
                                },
                                splitLine: {
                                    lineStyle: {
                                        color: '#f0f0f0',
                                        type: 'dashed'
                                    }
                                },
                                max: Math.ceil(maxValue * 1.1)
                            },
                            yAxis: {
                                type: 'category',
                                data: categories,
                                inverse: true,
                                axisLabel: {
                                    formatter: function(value) {
                                        if (value.length > 12) {
                                            return value.substring(0, 12) + '...';
                                        }
                                        return value;
                                    },
                                    interval: 0,
                                    rotate: 0,
                                    fontSize: 11,
                                    color: '#666'
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: '#e0e0e0'
                                    }
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            series: [
                                {
                                    name: type === 'income' ? '收入' : '支出',
                                    type: 'bar',
                                    data: values.map((value, index) => ({
                                        value: value,
                                        itemStyle: {
                                            color: {
                                                type: 'linear',
                                                x: 0, y: 0, x2: 1, y2: 0,
                                                colorStops: type === 'income' ? [
                                                    { offset: 0, color: '#4CAF50' },
                                                    { offset: 1, color: '#81C784' }
                                                ] : [
                                                    { offset: 0, color: '#FF7043' },
                                                    { offset: 1, color: '#FFAB91' }
                                                ]
                                            },
                                            borderRadius: [0, 6, 6, 0],
                                            shadowBlur: 4,
                                            shadowColor: 'rgba(0, 0, 0, 0.1)',
                                            shadowOffsetX: 2,
                                            shadowOffsetY: 2
                                        }
                                    })),
                                    label: {
                                        show: true,
                                        position: 'right',
                                        formatter: function(params) {
                                            return '¥' + params.value.toFixed(2);
                                        },
                                        fontSize: 11,
                                        color: '#555',
                                        fontWeight: '500'
                                    },
                                    emphasis: {
                                        itemStyle: {
                                            shadowBlur: 8,
                                            shadowColor: 'rgba(0, 0, 0, 0.2)'
                                        }
                                    },
                                    barWidth: '70%'
                                }
                            ]
                        };
                        
                        console.log(`设置${type}排行榜图表选项:`, option);
                        
                        try {
                            rankingChart.setOption(option, true);
                            console.log(`${type}排行榜图表渲染完成`);
                            
                            // 额外的强制重绘步骤
                            setTimeout(() => {
                                if (rankingChart) {
                                    rankingChart.resize();
                                }
                            }, 200);
                        } catch (err) {
                            console.error(`渲染${type}排行榜时出错:`, err);
                            showRankingError(`渲染图表失败: ${err.message}`);
                        }
                    }, 100);
                    
                    // 响应窗口大小变化
                    window.addEventListener('resize', function() {
                        try {
                            if (rankingChart) {
                                rankingChart.resize();
                            }
                        } catch (err) {
                            console.error("图表调整大小时出错:", err);
                        }
                    });
                    
                    return rankingChart;
                } catch (error) {
                    console.error(`初始化${type}排行榜出错:`, error);
                    showRankingError(`初始化排行榜失败: ${error.message}`);
                    return null;
                }
            }
            
            // 更新排行榜标题
            function updateRankingTitle() {
                const typeText = currentRankingType === 'income' ? '收入排行榜' : '支出排行榜';
                const sortText = currentSortOrder === 'desc' ? '(降序)' : '(升序)';
                document.getElementById('rankingTitle').textContent = `${typeText} ${sortText}`;
            }

            // 更新排序按钮显示
            function updateSortButton() {
                const sortIcon = document.getElementById('sortIcon');
                const sortText = document.getElementById('sortText');

                if (sortIcon && sortText) {
                    if (currentSortOrder === 'desc') {
                        sortIcon.className = 'bi bi-sort-numeric-down';
                        sortText.textContent = '降序';
                    } else {
                        sortIcon.className = 'bi bi-sort-numeric-up';
                        sortText.textContent = '升序';
                    }
                }
            }

            // 设置排行榜相关事件
            function setupRankingEvents() {
                console.log("设置排行榜事件监听器");
                
                // 支出排行榜按钮点击事件
                const expenseBtn = document.getElementById('expenseRankingBtn');
                if (expenseBtn) {
                    expenseBtn.onclick = function(e) {
                        e.preventDefault(); // 阻止默认行为
                        console.log("点击了支出排行榜按钮");
                        expenseBtn.classList.add('active', 'btn-primary');
                        expenseBtn.classList.remove('btn-outline-primary');
                        
                        const incomeBtn = document.getElementById('incomeRankingBtn');
                        if (incomeBtn) {
                            incomeBtn.classList.remove('active', 'btn-primary');
                            incomeBtn.classList.add('btn-outline-primary');
                        }
                        
                        currentRankingType = 'expense';
                        updateRankingTitle();
                        updateSortButton();
                        updateRankingCount('expense');
                        initRankingChart('expense');
                        return false;
                    };
                } else {
                    console.error("找不到支出排行榜按钮元素");
                }
                
                // 收入排行榜按钮点击事件
                const incomeBtn = document.getElementById('incomeRankingBtn');
                if (incomeBtn) {
                    incomeBtn.onclick = function(e) {
                        e.preventDefault(); // 阻止默认行为
                        console.log("点击了收入排行榜按钮");
                        incomeBtn.classList.add('active', 'btn-primary');
                        incomeBtn.classList.remove('btn-outline-primary');
                        
                        const expenseBtn = document.getElementById('expenseRankingBtn');
                        if (expenseBtn) {
                            expenseBtn.classList.remove('active', 'btn-primary');
                            expenseBtn.classList.add('btn-outline-primary');
                        }
                        
                        currentRankingType = 'income';
                        updateRankingTitle();
                        updateSortButton();
                        updateRankingCount('income');
                        initRankingChart('income');
                        return false;
                    };
                } else {
                    console.error("找不到收入排行榜按钮元素");
                }
                
                // 标签页切换事件
                const rankingTab = document.getElementById('pills-ranking-tab');
                if (rankingTab) {
                    rankingTab.addEventListener('shown.bs.tab', function(e) {
                        console.log("排行榜标签页被激活");
                        setTimeout(() => {
                            initRankingChart(currentRankingType);
                        }, 200);
                    });
                } else {
                    console.error("找不到排行榜标签页元素");
                }

                // 排序切换按钮点击事件
                const sortToggleBtn = document.getElementById('sortToggleBtn');
                if (sortToggleBtn) {
                    sortToggleBtn.onclick = function(e) {
                        e.preventDefault();
                        console.log("点击了排序切换按钮");

                        // 切换排序状态
                        currentSortOrder = currentSortOrder === 'desc' ? 'asc' : 'desc';

                        // 更新按钮显示
                        updateSortButton();

                        // 更新标题并重新渲染图表
                        updateRankingTitle();
                        initRankingChart(currentRankingType);
                        return false;
                    };
                } else {
                    console.error("找不到排序切换按钮元素");
                }
            }

            // 初始化月度收支对比图
            function initMonthlyChart(monthlyData) {
                const chartDom = document.getElementById('monthlyChart');
                const myChart = echarts.init(chartDom);

                if (!monthlyData || Object.keys(monthlyData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center'
                        }
                    });
                    return;
                }

                const months = Object.keys(monthlyData).sort();
                const incomeData = months.map(month => monthlyData[month].income || 0);
                const expenseData = months.map(month => Math.abs(monthlyData[month].expense || 0));

                const option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    legend: {
                        data: ['收入', '支出']
                    },
                    xAxis: {
                        type: 'category',
                        data: months
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: '¥{value}'
                        }
                    },
                    series: [
                        {
                            name: '收入',
                            type: 'bar',
                            data: incomeData,
                            itemStyle: {
                                color: '#28a745'
                            }
                        },
                        {
                            name: '支出',
                            type: 'bar',
                            data: expenseData,
                            itemStyle: {
                                color: '#dc3545'
                            }
                        }
                    ]
                };

                myChart.setOption(option);
            }

            // 初始化账户分布饼图
            function initAccountChart(accountData) {
                const chartDom = document.getElementById('accountChart');
                const myChart = echarts.init(chartDom);

                if (!accountData || Object.keys(accountData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center'
                        }
                    });
                    return;
                }

                const seriesData = Object.entries(accountData).map(([account, amount]) => ({
                    name: account || '未知账户',
                    value: Math.abs(amount)
                }));

                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left'
                    },
                    series: [
                        {
                            name: '账户分布',
                            type: 'pie',
                            radius: '50%',
                            data: seriesData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };

                myChart.setOption(option);
            }

            // 初始化周收支热力图
            function initWeeklyHeatmap(weeklyData) {
                const chartDom = document.getElementById('weeklyHeatmap');
                const myChart = echarts.init(chartDom);

                if (!weeklyData || weeklyData.length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center'
                        }
                    });
                    return;
                }

                const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                const hours = Array.from({length: 24}, (_, i) => i + '时');

                const option = {
                    tooltip: {
                        position: 'top',
                        formatter: function(params) {
                            return `${weekdays[params.data[1]]} ${hours[params.data[0]]}<br/>交易次数: ${params.data[2]}`;
                        }
                    },
                    grid: {
                        height: '50%',
                        top: '10%'
                    },
                    xAxis: {
                        type: 'category',
                        data: hours,
                        splitArea: {
                            show: true
                        }
                    },
                    yAxis: {
                        type: 'category',
                        data: weekdays,
                        splitArea: {
                            show: true
                        }
                    },
                    visualMap: {
                        min: 0,
                        max: Math.max(...weeklyData.map(item => item[2])),
                        calculable: true,
                        orient: 'horizontal',
                        left: 'center',
                        bottom: '15%'
                    },
                    series: [{
                        name: '交易活跃度',
                        type: 'heatmap',
                        data: weeklyData,
                        label: {
                            show: true
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }]
                };

                myChart.setOption(option);
            }

            // 初始化收支累计图
            function initCumulativeChart(cumulativeData) {
                const chartDom = document.getElementById('cumulativeChart');
                const myChart = echarts.init(chartDom);

                if (!cumulativeData || Object.keys(cumulativeData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center'
                        }
                    });
                    return;
                }

                const dates = Object.keys(cumulativeData).sort();
                const values = dates.map(date => cumulativeData[date]);

                const option = {
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            return `${params[0].axisValue}<br/>累计金额: ¥${params[0].data.toFixed(2)}`;
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: dates
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: '¥{value}'
                        }
                    },
                    series: [
                        {
                            data: values,
                            type: 'line',
                            smooth: true,
                            lineStyle: {
                                width: 3
                            },
                            areaStyle: {
                                opacity: 0.3
                            },
                            itemStyle: {
                                color: '#007bff'
                            }
                        }
                    ]
                };

                myChart.setOption(option);
            }

            // 初始化分类收支雷达图
            function initRadarChart(categoryData, typeData) {
                const chartDom = document.getElementById('radarChart');
                const myChart = echarts.init(chartDom);

                if (!categoryData || Object.keys(categoryData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center'
                        }
                    });
                    return;
                }

                // 获取前6个最大的分类
                const sortedCategories = Object.entries(categoryData)
                    .sort((a, b) => Math.abs(b[1]) - Math.abs(a[1]))
                    .slice(0, 6);

                const indicator = sortedCategories.map(([category, _]) => ({
                    name: category,
                    max: Math.max(...sortedCategories.map(([_, amount]) => Math.abs(amount)))
                }));

                const data = sortedCategories.map(([_, amount]) => Math.abs(amount));

                const option = {
                    tooltip: {},
                    radar: {
                        indicator: indicator
                    },
                    series: [{
                        name: '分类金额',
                        type: 'radar',
                        data: [
                            {
                                value: data,
                                name: '支出分布'
                            }
                        ]
                    }]
                };

                myChart.setOption(option);
            }

            // 初始化时段分析柱状图
            function initHourlyChart(hourlyData) {
                const chartDom = document.getElementById('hourlyChart');
                const myChart = echarts.init(chartDom);

                if (!hourlyData || Object.keys(hourlyData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center'
                        }
                    });
                    return;
                }

                const hours = Array.from({length: 24}, (_, i) => i + '时');
                const data = hours.map(hour => {
                    const hourKey = hour.replace('时', '');
                    return hourlyData[hourKey] || 0;
                });

                const option = {
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            return `${params[0].axisValue}<br/>交易次数: ${params[0].data}`;
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: hours
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: [
                        {
                            data: data,
                            type: 'bar',
                            itemStyle: {
                                color: function(params) {
                                    // 根据时段设置不同颜色
                                    const hour = params.dataIndex;
                                    if (hour >= 6 && hour < 12) return '#ffc107'; // 上午
                                    if (hour >= 12 && hour < 18) return '#28a745'; // 下午
                                    if (hour >= 18 && hour < 22) return '#fd7e14'; // 晚上
                                    return '#6c757d'; // 深夜/凌晨
                                }
                            }
                        }
                    ]
                };

                myChart.setOption(option);
            }
        });
    </script>
</body>
</html>
